import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { makeFetchCalls } from '../helpers';
import type {
  ActionConfig,
  ExtendedActionConfig,
} from '../models/action.config';
import { useClientAction } from './useClientActions';

// Mock dependencies to avoid complex hook interactions
vi.mock('../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
}));

vi.mock('../helpers/render-template', () => ({
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

vi.mock('../useAppStore', () => ({
  useAppStore: {
    getState: vi.fn(() => ({ mockState: 'test' })),
  },
}));

vi.mock('./useModalStore', () => ({
  useModalStore: vi.fn(() => vi.fn()),
}));

vi.mock('./useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn(() => vi.fn()),
}));

vi.mock('./useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('./useNotesStore', () => ({
  useNotesStore: {
    getState: vi.fn(() => ({
      addNote: vi.fn(),
    })),
  },
}));

vi.mock('./useReminderStore', () => ({
  useReminderStore: {
    getState: vi.fn(() => ({
      addReminder: vi.fn(),
    })),
  },
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    getValues: vi.fn(() => ({ testField: 'testValue' })),
    resetField: vi.fn(),
    watch: vi.fn(() => ({ testField: 'testValue' })),
    formState: {},
  })),
}));

describe('useClientActions - Execution Pattern Tests', () => {
  let mockNavigate: ReturnType<typeof vi.fn>;
  let mockLocation: { search: string };
  let mockKeycloak: { token: string };
  let executionOrder: string[];
  let executionTimes: Record<string, number>;

  beforeEach(() => {
    mockNavigate = vi.fn();
    mockLocation = { search: '' };
    mockKeycloak = { token: 'mock-token' };
    executionOrder = [];
    executionTimes = {};
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // Helper to track execution order and timing
  const trackExecution = (id: string) => {
    executionOrder.push(id);
    executionTimes[id] = Date.now();
  };

  // Mock console.log to track execution
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation((...args) => {
      if (typeof args[0] === 'string') {
        trackExecution(args[0]);
      }
    });
  });

  // Action creators for different types of simple actions
  const createLogAction = (id: string): ActionConfig => ({
    type: 'clientAction',
    action: 'log',
    payload: [id],
  });

  const createAsyncLogAction = (id: string): ExtendedActionConfig => ({
    type: 'clientAction',
    action: 'log',
    payload: [id],
    async: true,
  });

  const createConditionalAction = (
    id: string,
    condition = true
  ): ActionConfig => ({
    type: 'clientAction',
    action: 'conditional',
    payload: {
      condition,
      actions: {
        whenTrue: [createLogAction(id)],
        whenFalse: [createLogAction(`${id}-false`)],
      },
    },
  });

  const createTriggerFetchAction = (id: string): ActionConfig => ({
    type: 'clientAction',
    action: 'triggerFetchCall',
    payload: [
      {
        key: id,
        url: '/api/test',
        method: 'GET',
      },
    ],
  });

  describe('Refactored callClientAction Factory Function - All Cases', () => {
    it('should handle Case 1: Array with concurrency limit', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('concurrent1'),
        createLogAction('concurrent2'),
        createLogAction('concurrent3'),
        createLogAction('concurrent4'),
      ];

      await act(async () => {
        await result.current.callClientAction(actions, false, 2); // concurrencyLimit = 2
      });

      expect(executionOrder).toEqual([
        'concurrent1',
        'concurrent2',
        'concurrent3',
        'concurrent4',
      ]);
    });

    it('should handle Case 2: Array with async=true (sequential)', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('seq1'),
        createLogAction('seq2'),
        createLogAction('seq3'),
      ];

      await act(async () => {
        await result.current.callClientAction(actions, true); // async = true
      });

      expect(executionOrder).toEqual(['seq1', 'seq2', 'seq3']);

      // Verify sequential timing
      expect(executionTimes['seq2']).toBeGreaterThan(executionTimes['seq1']);
      expect(executionTimes['seq3']).toBeGreaterThan(executionTimes['seq2']);
    });

    it('should handle Case 3: Array with async=false or undefined (concurrent)', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('conc1'),
        createLogAction('conc2'),
        createLogAction('conc3'),
      ];

      await act(async () => {
        await result.current.callClientAction(actions); // async = undefined (default)
      });

      expect(executionOrder).toEqual(['conc1', 'conc2', 'conc3']);
    });

    it('should handle Case 4: Single action with explicit async property', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const asyncAction = createAsyncLogAction('explicit-async');

      await act(async () => {
        const promise = result.current.callClientAction(asyncAction);
        expect(promise).toBeInstanceOf(Promise);
        await promise;
      });

      expect(executionOrder).toContain('explicit-async');
    });

    it('should handle Case 5: Single action with async parameter override', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const action = createLogAction('param-async');

      await act(async () => {
        const promise = result.current.callClientAction(action, true); // async = true
        expect(promise).toBeInstanceOf(Promise);
        await promise;
      });

      expect(executionOrder).toContain('param-async');
    });

    it('should handle Case 6: Single action - synchronous execution (default)', () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const action = createLogAction('sync-default');

      const returnValue = result.current.callClientAction(action); // no async parameter
      expect(returnValue).toBeUndefined();
      expect(executionOrder).toContain('sync-default');
    });

    it('should handle empty arrays correctly', () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const returnValue = result.current.callClientAction([]);
      expect(returnValue).toBeUndefined();
    });

    it('should prioritize concurrencyLimit over async parameter for arrays', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('priority1'),
        createLogAction('priority2'),
        createLogAction('priority3'),
        createLogAction('priority4'),
      ];

      await act(async () => {
        // Both async=true and concurrencyLimit=2, concurrencyLimit should take precedence
        await result.current.callClientAction(actions, true, 2);
      });

      expect(executionOrder).toEqual([
        'priority1',
        'priority2',
        'priority3',
        'priority4',
      ]);
    });

    it('should handle async property precedence over parameter for single actions', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const asyncAction = createAsyncLogAction('precedence-test');

      await act(async () => {
        // async property should take precedence over async=false parameter
        const promise = result.current.callClientAction(asyncAction, false);
        expect(promise).toBeInstanceOf(Promise);
        await promise;
      });

      expect(executionOrder).toContain('precedence-test');
    });
  });

  describe('Testing Different Action Types', () => {
    it('should execute conditional actions correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const conditionalAction = createConditionalAction(
        'conditional-true',
        true
      );

      await act(async () => {
        await result.current.callClientActionAsync(conditionalAction);
      });

      expect(executionOrder).toContain('conditional-true');
    });

    it('should execute triggerFetchCall actions correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const fetchAction = createTriggerFetchAction('fetch-test');

      await act(async () => {
        await result.current.callClientActionAsync(fetchAction);
      });

      // Verify the fetch was called (mocked)
      expect(vi.mocked(makeFetchCalls)).toHaveBeenCalled();
    });

    it('should execute timeout actions correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const timeoutAction: ActionConfig = {
        type: 'clientAction',
        action: 'timeout',
        payload: [10, [createLogAction('timeout-test')]],
      };

      result.current.executeClientActionSynchronously(timeoutAction);

      // The timeout action should execute the nested action
      expect(executionOrder).toContain('timeout-test');
    });
  });

  describe('Execution Pattern Verification', () => {
    it('should execute actions sequentially when using callClientActionsSequentially', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('seq1'),
        createLogAction('seq2'),
        createLogAction('seq3'),
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });

      expect(executionOrder).toEqual(['seq1', 'seq2', 'seq3']);
      // Verify sequential timing
      expect(executionTimes['seq2']).toBeGreaterThan(executionTimes['seq1']);
      expect(executionTimes['seq3']).toBeGreaterThan(executionTimes['seq2']);
    });

    it('should execute actions concurrently when using callClientActionsConcurrently', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('conc1'),
        createLogAction('conc2'),
        createLogAction('conc3'),
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      expect(executionOrder).toEqual(['conc1', 'conc2', 'conc3']);
    });

    it('should execute actions with controlled concurrency when using callClientActionsWithLimit', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actions = [
        createLogAction('limit1'),
        createLogAction('limit2'),
        createLogAction('limit3'),
        createLogAction('limit4'),
      ];

      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 2);
      });

      expect(executionOrder).toEqual(['limit1', 'limit2', 'limit3', 'limit4']);
    });
  });

  describe('callClientActionsWithLimit', () => {
    it('should respect concurrency limit', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createLogAction('limit1'),
        createLogAction('limit2'),
        createLogAction('limit3'),
        createLogAction('limit4'),
      ];

      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 2);
      });
      const endTime = Date.now();

      expect(executionOrder).toEqual(['limit1', 'limit2', 'limit3', 'limit4']);

      // With limit of 2, should take roughly 2 batches: 60ms total
      expect(endTime - startTime).toBeGreaterThan(50);
      expect(endTime - startTime).toBeLessThan(80);
    });

    it('should use default concurrency limit of 3', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = Array.from({ length: 6 }, (_, i) =>
        createLogAction(`default${i + 1}`)
      );

      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsWithLimit(actions);
      });
      const endTime = Date.now();

      // With default limit of 3, should take 2 batches: ~40ms total
      expect(endTime - startTime).toBeGreaterThan(30);
      expect(endTime - startTime).toBeLessThan(60);
    });
  });

  describe('callClientAction with async property', () => {
    it('should return Promise when async: true', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const asyncAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['async-test'],
        async: true,
      };

      const promise = result.current.callClientAction(asyncAction);
      expect(promise).toBeInstanceOf(Promise);

      await promise;
      expect(executionOrder).toContain('async-test');
    });

    it('should execute synchronously when async is false or undefined', () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const syncAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['sync-test'],
      };

      const returnValue = result.current.callClientAction(syncAction);
      expect(returnValue).toBeUndefined();
      expect(executionOrder).toContain('sync-test');
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle form submission workflow correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Simulate form submission: validate → submit → navigate
      await act(async () => {
        await result.current.callClientActionsSequentially([
          createLogAction('validate-form'),
          createLogAction('submit-data'),
          createLogAction('navigate-success'),
        ]);
      });

      expect(executionOrder).toEqual([
        'validate-form',
        'submit-data',
        'navigate-success',
      ]);

      // Verify sequential execution
      expect(executionTimes['submit-data']).toBeGreaterThan(
        executionTimes['validate-form']
      );
      expect(executionTimes['navigate-success']).toBeGreaterThan(
        executionTimes['submit-data']
      );
    });

    it('should handle data loading workflow correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Simulate concurrent data loading
      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsConcurrently([
          createLogAction('load-user-data'),
          createLogAction('load-preferences'),
          createLogAction('load-notifications'),
        ]);
      });
      const endTime = Date.now();

      expect(executionOrder).toEqual([
        'load-user-data',
        'load-preferences',
        'load-notifications',
      ]);

      // Should complete in roughly the time of the longest action
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('should handle mixed sequential and concurrent workflow', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      await act(async () => {
        // Step 1: Sequential setup
        await result.current.callClientActionsSequentially([
          createLogAction('init-app'),
          createLogAction('setup-auth'),
        ]);

        // Step 2: Concurrent data loading
        await result.current.callClientActionsConcurrently([
          createLogAction('load-data-1'),
          createLogAction('load-data-2'),
        ]);

        // Step 3: Final sequential cleanup
        await result.current.callClientActionAsync(createLogAction('cleanup'));
      });

      expect(executionOrder).toEqual([
        'init-app',
        'setup-auth',
        'load-data-1',
        'load-data-2',
        'cleanup',
      ]);

      // Verify setup is sequential
      expect(executionTimes['setup-auth']).toBeGreaterThan(
        executionTimes['init-app']
      );

      // Verify data loading starts after setup
      expect(executionTimes['load-data-1']).toBeGreaterThan(
        executionTimes['setup-auth']
      );
      expect(executionTimes['load-data-2']).toBeGreaterThan(
        executionTimes['setup-auth']
      );

      // Verify cleanup happens after data loading
      expect(executionTimes['cleanup']).toBeGreaterThan(
        executionTimes['load-data-1']
      );
      expect(executionTimes['cleanup']).toBeGreaterThan(
        executionTimes['load-data-2']
      );
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle Promise.allSettled pattern for partial failures', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const errorAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [],
      };

      const actions = [
        createLogAction('success1'),
        errorAction,
        createLogAction('success2'),
      ];

      // Use Promise.allSettled to handle partial failures
      const promises = actions.map((action) =>
        result.current
          .callClientActionAsync(action)
          .catch((error) => ({ error }))
      );

      const results = await Promise.allSettled(promises);

      expect(results).toHaveLength(3);
      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('fulfilled'); // Caught error
      expect(results[2].status).toBe('fulfilled');

      expect(executionOrder).toContain('success1');
      expect(executionOrder).toContain('success2');
    });

    it('should handle empty action arrays', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      await expect(
        result.current.callClientActionsSequentially([])
      ).resolves.toBeUndefined();

      await expect(
        result.current.callClientActionsConcurrently([])
      ).resolves.toBeUndefined();

      await expect(
        result.current.callClientActionsWithLimit([])
      ).resolves.toBeUndefined();
    });

    it('should handle single action in array functions', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const singleAction = [createLogAction('single')];

      await result.current.callClientActionsSequentially(singleAction);
      await result.current.callClientActionsConcurrently(singleAction);
      await result.current.callClientActionsWithLimit(singleAction);

      expect(
        executionOrder.filter((action) => action === 'single')
      ).toHaveLength(3);
    });

    it('should demonstrate the difference between concurrent and sequential timing', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createLogAction('timing1'),
        createLogAction('timing2'),
        createLogAction('timing3'),
      ];

      // Test concurrent execution
      const concurrentStart = Date.now();
      await result.current.callClientActionsConcurrently([...actions]);
      const concurrentTime = Date.now() - concurrentStart;

      // Reset execution tracking
      executionOrder.length = 0;

      // Test sequential execution
      const sequentialStart = Date.now();
      await result.current.callClientActionsSequentially([...actions]);
      const sequentialTime = Date.now() - sequentialStart;

      // Concurrent should be much faster than sequential
      expect(concurrentTime).toBeLessThan(sequentialTime);
      expect(concurrentTime).toBeLessThan(80); // ~50ms for concurrent
      expect(sequentialTime).toBeGreaterThan(140); // ~150ms for sequential
    });
  });

  describe('Refactored callClientAction Factory Function', () => {
    it('should handle Case 1: Array with concurrency limit', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createLogAction('concurrent1'),
        createLogAction('concurrent2'),
        createLogAction('concurrent3'),
        createLogAction('concurrent4'),
      ];

      const startTime = Date.now();
      await result.current.callClientAction(actions, false, 2); // concurrencyLimit = 2
      const endTime = Date.now();

      expect(executionOrder).toEqual([
        'concurrent1',
        'concurrent2',
        'concurrent3',
        'concurrent4',
      ]);

      // Should take roughly 2 batches of 20ms each = ~40ms
      expect(endTime - startTime).toBeGreaterThan(35);
      expect(endTime - startTime).toBeLessThan(60);
    });

    it('should handle Case 2: Array with async=true (sequential)', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createLogAction('seq1'),
        createLogAction('seq2'),
        createLogAction('seq3'),
      ];

      const startTime = Date.now();
      await result.current.callClientAction(actions, true); // async = true
      const endTime = Date.now();

      expect(executionOrder).toEqual(['seq1', 'seq2', 'seq3']);

      // Should take roughly 30ms (sequential)
      expect(endTime - startTime).toBeGreaterThan(25);
      expect(endTime - startTime).toBeLessThan(50);

      // Verify sequential timing
      expect(executionTimes['seq2']).toBeGreaterThan(executionTimes['seq1']);
      expect(executionTimes['seq3']).toBeGreaterThan(executionTimes['seq2']);
    });

    it('should handle Case 3: Array with async=false or undefined (concurrent)', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createLogAction('conc1'),
        createLogAction('conc2'),
        createLogAction('conc3'),
      ];

      const startTime = Date.now();
      await result.current.callClientAction(actions); // async = undefined (default)
      const endTime = Date.now();

      expect(executionOrder).toEqual(['conc1', 'conc2', 'conc3']);

      // Should take roughly 20ms (concurrent)
      expect(endTime - startTime).toBeLessThan(40);
    });

    it('should handle Case 4: Single action with explicit async property', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const asyncAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['explicit-async'],
        async: true,
      };

      const promise = result.current.callClientAction(asyncAction);
      expect(promise).toBeInstanceOf(Promise);

      await promise;
      expect(executionOrder).toContain('explicit-async');
    });

    it('should handle Case 5: Single action with async parameter override', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['param-async'],
      };

      const promise = result.current.callClientAction(action, true); // async = true
      expect(promise).toBeInstanceOf(Promise);

      await promise;
      expect(executionOrder).toContain('param-async');
    });

    it('should handle Case 6: Single action - synchronous execution (default)', () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['sync-default'],
      };

      const returnValue = result.current.callClientAction(action); // no async parameter
      expect(returnValue).toBeUndefined();
      expect(executionOrder).toContain('sync-default');
    });

    it('should handle empty arrays correctly', () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const returnValue = result.current.callClientAction([]);
      expect(returnValue).toBeUndefined();
    });

    it('should prioritize concurrencyLimit over async parameter for arrays', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createLogAction('priority1'),
        createLogAction('priority2'),
        createLogAction('priority3'),
        createLogAction('priority4'),
      ];

      const startTime = Date.now();
      // Both async=true and concurrencyLimit=2, concurrencyLimit should take precedence
      await result.current.callClientAction(actions, true, 2);
      const endTime = Date.now();

      expect(executionOrder).toEqual([
        'priority1',
        'priority2',
        'priority3',
        'priority4',
      ]);

      // Should use concurrency limit (batched) rather than sequential
      // 2 batches of 15ms each = ~30ms, not 60ms for sequential
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('should handle async property precedence over parameter for single actions', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const asyncAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['precedence-test'],
        async: true,
      };

      // async property should take precedence over async=false parameter
      const promise = result.current.callClientAction(asyncAction, false);
      expect(promise).toBeInstanceOf(Promise);

      await promise;
      expect(executionOrder).toContain('precedence-test');
    });
  });

  describe('Performance and Memory Tests', () => {
    it('should handle large number of actions efficiently', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const largeActionSet = Array.from({ length: 100 }, (_, i) =>
        createLogAction(`bulk${i}`)
      );

      const startTime = Date.now();

      // Test with concurrency limit to prevent overwhelming the system
      await result.current.callClientActionsWithLimit(largeActionSet, 10);

      const endTime = Date.now();

      expect(executionOrder).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(200); // Should complete reasonably fast
    });

    it('should properly clean up resources and not leak memory', async () => {
      const { result, unmount } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Execute some actions
      await result.current.callClientActionAsync(
        createLogAction('cleanup-test')
      );

      // Unmount should not cause any issues
      expect(() => unmount()).not.toThrow();
    });
  });
});
